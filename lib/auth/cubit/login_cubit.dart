import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';

part 'login_state.dart';

/// {@template login_cubit}
/// A [Cubit] which manages the login form state.
/// {@endtemplate}
class LoginCubit extends Cubit<LoginState> {
  /// {@macro login_cubit}
  LoginCubit(this._authRepository) : super(const LoginState());

  final AuthRepository _authRepository;

  /// Updates the email input.
  void emailChanged(String value) {
    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: _getFormStatus(email, state.password),
      ),
    );
  }

  /// Updates the password input.
  void passwordChanged(String value) {
    final password = Password.dirty(value);
    emit(
      state.copyWith(
        password: password,
        status: _getFormStatus(state.email, password),
      ),
    );
  }

  /// Gets the form status based on validation
  FormStatus _getFormStatus(Email email, Password password) {
    return email.isValid && password.isValid
        ? FormStatus.valid
        : FormStatus.invalid;
  }

  /// Submits the login form.
  Future<void> logInWithCredentials() async {
    if (!state.status.isValidated) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));
    try {
      await _authRepository.logInWithEmailAndPassword(
        email: state.email.value,
        password: state.password.value,
      );
      emit(state.copyWith(status: FormStatus.submissionSuccess));
    } on LogInWithEmailAndPasswordFailure catch (e) {
      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
