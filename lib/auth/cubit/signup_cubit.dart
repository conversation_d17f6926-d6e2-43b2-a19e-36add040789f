import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import 'package:bloomg_flutter/auth/models/confirmed_password.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/name.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/enums/form_status.dart';

part 'signup_state.dart';

/// {@template signup_cubit}
/// A [Cubit] which manages the signup form state.
/// {@endtemplate}
class SignupCubit extends Cubit<SignupState> {
  /// {@macro signup_cubit}
  SignupCubit(this._authRepository) : super(const SignupState());

  final AuthRepository _authRepository;

  /// Updates the name input.
  void nameChanged(String value) {
    final name = Name.dirty(value);
    emit(
      state.copyWith(
        name: name,
        status: _getFormStatus(
          name,
          state.email,
          state.password,
          state.confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the email input.
  void emailChanged(String value) {
    final email = Email.dirty(value);
    emit(
      state.copyWith(
        email: email,
        status: _getFormStatus(
          state.name,
          email,
          state.password,
          state.confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the password input.
  void passwordChanged(String value) {
    final password = Password.dirty(value);
    final confirmedPassword = ConfirmedPassword.dirty(
      password: password.value,
      value: state.confirmedPassword.value,
    );
    emit(
      state.copyWith(
        password: password,
        confirmedPassword: confirmedPassword,
        status: _getFormStatus(
          state.name,
          state.email,
          password,
          confirmedPassword,
        ),
      ),
    );
  }

  /// Updates the confirmed password input.
  void confirmedPasswordChanged(String value) {
    final confirmedPassword = ConfirmedPassword.dirty(
      password: state.password.value,
      value: value,
    );
    emit(
      state.copyWith(
        confirmedPassword: confirmedPassword,
        status: _getFormStatus(
          state.name,
          state.email,
          state.password,
          confirmedPassword,
        ),
      ),
    );
  }

  /// Gets the form status based on validation
  FormStatus _getFormStatus(
    Name name,
    Email email,
    Password password,
    ConfirmedPassword confirmedPassword,
  ) {
    return name.isValid &&
            email.isValid &&
            password.isValid &&
            confirmedPassword.isValid
        ? FormStatus.valid
        : FormStatus.invalid;
  }

  /// Submits the signup form.
  Future<void> signUpFormSubmitted() async {
    if (!state.status.isValidated) return;
    emit(state.copyWith(status: FormStatus.submissionInProgress));
    try {
      await _authRepository.signUp(
        email: state.email.value,
        password: state.password.value,
        name: state.name.value,
      );
      emit(state.copyWith(status: FormStatus.submissionSuccess));
    } on SignUpWithEmailAndPasswordFailure catch (e) {
      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormStatus.submissionFailure,
        ),
      );
    } catch (_) {
      emit(state.copyWith(status: FormStatus.submissionFailure));
    }
  }
}
