import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:bloomg_flutter/auth/cubit/login_cubit.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository_impl.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/navigation/auth_navigation.dart';
import 'package:bloomg_flutter/shared/widgets/widgets.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocProvider(
        create: (_) => LoginCubit(const AuthRepositoryImpl()),
        child: const LoginForm(),
      ),
    );
  }
}

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _emailController.addListener(() {
      context.read<LoginCubit>().emailChanged(_emailController.text);
    });
    _passwordController.addListener(() {
      context.read<LoginCubit>().passwordChanged(_passwordController.text);
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state.status.isSubmissionFailure) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Authentication Failure'),
                backgroundColor: AppColors.error,
              ),
            );
        }
        if (state.status.isSubmissionSuccess) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              const SnackBar(
                content: Text('Login successful!'),
                backgroundColor: AppColors.success,
              ),
            );
        }
      },
      child: SafeArea(
        child: Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
          child: Column(
            children: [
              const SizedBox(height: AppDimensions.spacingHuge),
              const BloomgLogo(),
              const SizedBox(height: AppDimensions.spacingMassive),
              // Login Form
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Login',
                      style: AppTextStyles.heading1,
                    ),
                    const SizedBox(height: AppDimensions.spacingXXXL),
                    // Login Form Container
                    AuthFormContainer(
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Email Field
                            BlocBuilder<LoginCubit, LoginState>(
                              buildWhen: (previous, current) =>
                                  previous.email != current.email,
                              builder: (context, state) {
                                return AuthFormField(
                                  label: 'Your Email Address',
                                  controller: _emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  validator: (_) => state.email.isNotValid
                                      ? 'Please enter a valid email'
                                      : null,
                                );
                              },
                            ),
                            const SizedBox(height: AppDimensions.spacingXL),
                            // Password Field
                            BlocBuilder<LoginCubit, LoginState>(
                              buildWhen: (previous, current) =>
                                  previous.password != current.password,
                              builder: (context, state) {
                                return PasswordField(
                                  label: 'Your Password',
                                  controller: _passwordController,
                                  validator: (_) => state.password.isNotValid
                                      ? 'Password must be at least 6 characters'
                                      : null,
                                );
                              },
                            ),
                            const SizedBox(height: AppDimensions.spacingXXL),
                            // Login Button
                            BlocBuilder<LoginCubit, LoginState>(
                              buildWhen: (previous, current) =>
                                  previous.status != current.status,
                              builder: (context, state) {
                                return AuthButton(
                                  text: 'Log in',
                                  isLoading:
                                      state.status.isSubmissionInProgress,
                                  onPressed: state.status.isValidated
                                      ? () => context
                                          .read<LoginCubit>()
                                          .logInWithCredentials()
                                      : null,
                                );
                              },
                            ),
                            const SizedBox(height: AppDimensions.spacingXL),
                            // Forgot Password Link
                            Center(
                              child: TextButton(
                                onPressed: () =>
                                    AuthNavigation.toForgotPassword(context),
                                child: const Text(
                                  'Forgot password?',
                                  style: AppTextStyles.linkPlain,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Support Information
                    Column(
                      children: [
                        const SupportFooter(),
                        const SizedBox(height: AppDimensions.spacingXL),
                        // Create Account Link
                        TextButton(
                          onPressed: () =>
                              AuthNavigation.toCreateAccount(context),
                          child: const Text(
                            "Don't have an account? Create one",
                            style: AppTextStyles.link,
                          ),
                        ),
                        const SizedBox(height: AppDimensions.spacingXXXL),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
