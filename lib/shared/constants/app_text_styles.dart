import 'package:flutter/material.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';

/// App text style constants used throughout the application
class AppTextStyles {
  AppTextStyles._();

  // Logo text style
  static const TextStyle logo = TextStyle(
    color: AppColors.textPrimary,
    fontSize: 18,
    fontWeight: FontWeight.w600,
    letterSpacing: 2,
  );

  // Heading styles
  static const TextStyle heading1 = TextStyle(
    color: AppColors.textPrimary,
    fontSize: 32,
    fontWeight: FontWeight.w300,
  );

  // Body text styles
  static const TextStyle bodyLarge = TextStyle(
    color: AppColors.textPrimary,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle bodyMedium = TextStyle(
    color: AppColors.textSecondary,
    fontSize: 16,
  );

  static const TextStyle bodySmall = TextStyle(
    color: AppColors.textSecondary,
    fontSize: 14,
  );

  // Button text styles
  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  // Link text styles
  static const TextStyle link = TextStyle(
    color: AppColors.link,
    fontSize: 14,
    decoration: TextDecoration.underline,
    decorationColor: AppColors.linkUnderline,
  );

  static const TextStyle linkPlain = TextStyle(
    color: AppColors.link,
    fontSize: 14,
  );

  // Input text style
  static const TextStyle input = TextStyle(
    color: AppColors.textPrimary,
  );
}
