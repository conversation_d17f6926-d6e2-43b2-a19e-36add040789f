import 'package:flutter/material.dart';
import 'package:bloomg_flutter/auth/view/create_account_page.dart';
import 'package:bloomg_flutter/auth/view/forgot_password_page.dart';
import 'package:bloomg_flutter/auth/view/login_page.dart';

/// Navigation helpers for authentication flow
class AuthNavigation {
  AuthNavigation._();

  /// Navigate to login page
  static void toLogin(BuildContext context) {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute<void>(
        builder: (context) => const LoginPage(),
      ),
      (route) => false,
    );
  }

  /// Navigate to create account page
  static void toCreateAccount(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute<void>(
        builder: (context) => const CreateAccountPage(),
      ),
    );
  }

  /// Navigate to forgot password page
  static void toForgotPassword(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute<void>(
        builder: (context) => const ForgotPasswordPage(),
      ),
    );
  }

  /// Navigate back to previous screen
  static void back(BuildContext context) {
    Navigator.pop(context);
  }
}
