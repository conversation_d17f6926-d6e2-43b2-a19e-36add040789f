import 'package:flutter/material.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';

/// Reusable support footer widget
class SupportFooter extends StatelessWidget {
  const SupportFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Text(
            "Questions? We're happy to help at",
            style: AppTextStyles.bodySmall,
          ),
          const SizedBox(height: AppDimensions.spacingXS),
          GestureDetector(
            onTap: () {
              // Handle support email tap
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Email support functionality not implemented yet',
                  ),
                  backgroundColor: AppColors.primary,
                ),
              );
            },
            child: const Text(
              '<EMAIL>',
              style: AppTextStyles.link,
            ),
          ),
        ],
      ),
    );
  }
}
