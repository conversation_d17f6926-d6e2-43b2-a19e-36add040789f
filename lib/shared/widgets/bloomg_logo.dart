import 'package:flutter/material.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';

/// Reusable Bloomg logo widget
class BloomgLogo extends StatelessWidget {
  const BloomgLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: AppDimensions.logoBarWidth,
          height: AppDimensions.logoBarHeight,
          color: AppColors.textPrimary,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        const Text(
          'Bloomg',
          style: AppTextStyles.logo,
        ),
      ],
    );
  }
}
